:root {
  font-family: 'JetBrains Mono', 'Courier New', monospace;
  line-height: 1.4;
  font-weight: 400;

  color-scheme: dark;
  color: #00ff00;
  background-color: #000;

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

* {
  box-sizing: border-box;
}

*::before,
*::after {
  box-sizing: border-box;
}

body {
  margin: 0;
  min-width: 320px;
  min-height: 100vh;
  background: #000;
  color: #00ff00;
  overflow: hidden;
  font-family: 'JetBrains Mono', 'Courier New', monospace;
}

#root {
  min-height: 100vh;
}

a {
  font-weight: 500;
  color: #646cff;
  text-decoration: inherit;
}

a:hover {
  color: #535bf2;
}

h1, h2, h3, h4, h5, h6 {
  margin: 0;
  font-weight: 600;
  line-height: 1.2;
}

p {
  margin: 0;
}

ul, ol {
  margin: 0;
  padding: 0;
}

button {
  border-radius: 8px;
  border: 1px solid transparent;
  padding: 0.6em 1.2em;
  font-size: 1em;
  font-weight: 500;
  font-family: inherit;
  background-color: #1a1a1a;
  color: #fff;
  cursor: pointer;
  transition: all 0.25s ease;
}

button:hover {
  border-color: #646cff;
}

button:focus,
button:focus-visible {
  outline: 2px solid #646cff;
  outline-offset: 2px;
}

/* Scrollbar styling */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #1a1a1a;
}

::-webkit-scrollbar-thumb {
  background: #646cff;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #535bf2;
}
