.card3d-container {
  width: 400px;
  min-width: 400px;
  height: 100vh;
  background: #0a0a0a;
  border-right: 2px solid #00ff00;
  display: flex;
  flex-direction: column;
  position: relative;
  overflow: hidden;
}

.card3d-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: 
    repeating-linear-gradient(
      90deg,
      transparent,
      transparent 2px,
      rgba(0, 255, 0, 0.02) 2px,
      rgba(0, 255, 0, 0.02) 4px
    );
  pointer-events: none;
  z-index: 1;
}

.card3d-container canvas {
  flex: 1;
  position: relative;
  z-index: 2;
}

.card3d-info {
  position: relative;
  z-index: 3;
  padding: 2rem;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(10px);
  border-top: 1px solid rgba(0, 255, 0, 0.3);
}

.card3d-header {
  text-align: center;
  margin-bottom: 1.5rem;
}

.card3d-header h2 {
  color: #00ff00;
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0 0 0.5rem 0;
  text-shadow: 0 0 10px rgba(0, 255, 0, 0.5);
  font-family: 'JetBrains Mono', 'Courier New', monospace;
}

.card3d-header p {
  color: #888;
  font-size: 1rem;
  margin: 0;
  font-family: 'JetBrains Mono', 'Courier New', monospace;
}

.card3d-instructions {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.instruction {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 0.75rem;
  background: rgba(0, 255, 0, 0.05);
  border: 1px solid rgba(0, 255, 0, 0.2);
  border-radius: 8px;
  transition: all 0.3s ease;
  font-family: 'JetBrains Mono', 'Courier New', monospace;
}

.instruction:hover {
  background: rgba(0, 255, 0, 0.1);
  transform: translateX(5px);
  box-shadow: 0 4px 12px rgba(0, 255, 0, 0.2);
}

.instruction-icon {
  font-size: 1.5rem;
  width: 30px;
  text-align: center;
}

.instruction span:last-child {
  color: #00ff00;
  font-size: 0.9rem;
  font-weight: 500;
}

/* Loading state */
.card3d-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #00ff00;
  font-family: 'JetBrains Mono', 'Courier New', monospace;
  font-size: 1.2rem;
}

.card3d-loading::after {
  content: '';
  animation: loading-dots 1.5s infinite;
}

@keyframes loading-dots {
  0%, 20% { content: '.'; }
  40% { content: '..'; }
  60% { content: '...'; }
  80%, 100% { content: ''; }
}

/* Glow effect for the container */
.card3d-container::after {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(45deg, #00ff00, #0080ff, #00ff00);
  border-radius: 0;
  z-index: -1;
  opacity: 0.1;
  filter: blur(8px);
}

/* Responsive design */
@media (max-width: 768px) {
  .card3d-container {
    width: 100%;
    min-width: unset;
    height: 50vh;
    border-right: none;
    border-bottom: 2px solid #00ff00;
  }
  
  .card3d-info {
    padding: 1rem;
  }
  
  .card3d-header h2 {
    font-size: 1.2rem;
  }
  
  .card3d-header p {
    font-size: 0.9rem;
  }
  
  .instruction {
    padding: 0.5rem;
    gap: 0.75rem;
  }
  
  .instruction-icon {
    font-size: 1.2rem;
    width: 25px;
  }
  
  .instruction span:last-child {
    font-size: 0.8rem;
  }
}

@media (max-width: 480px) {
  .card3d-container {
    height: 40vh;
  }
  
  .card3d-info {
    padding: 0.75rem;
  }
  
  .card3d-instructions {
    gap: 0.75rem;
  }
  
  .instruction {
    flex-direction: column;
    text-align: center;
    gap: 0.5rem;
  }
  
  .instruction-icon {
    width: auto;
  }
}

/* Animation for when the card is being dragged */
.card3d-container.dragging {
  box-shadow: 0 0 30px rgba(0, 255, 0, 0.3);
}

.card3d-container.dragging .card3d-info {
  background: rgba(0, 255, 0, 0.1);
}

/* Pulse animation for instructions */
.instruction {
  animation: instruction-pulse 3s infinite;
}

.instruction:nth-child(1) {
  animation-delay: 0s;
}

.instruction:nth-child(2) {
  animation-delay: 1s;
}

.instruction:nth-child(3) {
  animation-delay: 2s;
}

@keyframes instruction-pulse {
  0%, 90% { 
    border-color: rgba(0, 255, 0, 0.2);
    background: rgba(0, 255, 0, 0.05);
  }
  5% { 
    border-color: rgba(0, 255, 0, 0.5);
    background: rgba(0, 255, 0, 0.15);
  }
}

/* Terminal hint at the bottom */
.card3d-terminal-hint {
  position: absolute;
  bottom: 1rem;
  left: 50%;
  transform: translateX(-50%);
  color: #888;
  font-size: 0.8rem;
  text-align: center;
  font-family: 'JetBrains Mono', 'Courier New', monospace;
  animation: hint-blink 2s infinite;
}

@keyframes hint-blink {
  0%, 70% { opacity: 0.7; }
  35% { opacity: 1; }
}
