.about {
  padding: 6rem 0;
  background: #1a1a1a;
  position: relative;
}

.about-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
}

.about-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  align-items: center;
}

.section-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: #fff;
  margin-bottom: 2rem;
  position: relative;
}

.section-title::after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 0;
  width: 60px;
  height: 4px;
  background: linear-gradient(135deg, #646cff 0%, #9333ea 100%);
  border-radius: 2px;
}

.about-description {
  font-size: 1.1rem;
  line-height: 1.7;
  color: #d0d0d0;
  margin-bottom: 1.5rem;
}

.skills-section {
  margin-top: 2.5rem;
}

.skills-section h3 {
  color: #fff;
  font-size: 1.25rem;
  margin-bottom: 1rem;
  font-weight: 600;
}

.skills-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 0.75rem;
}

.skill-tag {
  background: rgba(100, 108, 255, 0.1);
  color: #646cff;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: 500;
  border: 1px solid rgba(100, 108, 255, 0.2);
  transition: all 0.3s ease;
}

.skill-tag:hover {
  background: rgba(100, 108, 255, 0.2);
  transform: translateY(-2px);
}

.about-visual {
  display: flex;
  justify-content: center;
  align-items: center;
}

.profile-card {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  padding: 2rem;
  text-align: center;
  max-width: 300px;
  width: 100%;
  transition: transform 0.3s ease;
}

.profile-card:hover {
  transform: translateY(-10px);
}

.profile-image {
  margin-bottom: 1.5rem;
}

.image-placeholder {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  background: linear-gradient(135deg, #646cff 0%, #9333ea 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto;
  font-size: 2rem;
  font-weight: 700;
  color: #fff;
}

.profile-info h3 {
  color: #fff;
  font-size: 1.5rem;
  margin-bottom: 0.5rem;
  font-weight: 600;
}

.profile-info p {
  color: #a0a0a0;
  margin-bottom: 1.5rem;
  font-size: 1rem;
}

.profile-stats {
  display: flex;
  justify-content: space-around;
  gap: 1rem;
}

.stat {
  text-align: center;
}

.stat-number {
  display: block;
  font-size: 1.5rem;
  font-weight: 700;
  color: #646cff;
  margin-bottom: 0.25rem;
}

.stat-label {
  font-size: 0.85rem;
  color: #a0a0a0;
}

@media (max-width: 768px) {
  .about-content {
    grid-template-columns: 1fr;
    gap: 3rem;
    text-align: center;
  }

  .section-title::after {
    left: 50%;
    transform: translateX(-50%);
  }

  .skills-grid {
    justify-content: center;
  }

  .profile-stats {
    flex-direction: column;
    gap: 1.5rem;
  }
}
