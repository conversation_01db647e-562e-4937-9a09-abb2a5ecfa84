import React from 'react'
import './About.css'

const About = () => {
  const skills = [
    'React', 'JavaScript', 'TypeScript', 'Node.js',
    'Three.js', 'WebGL', 'CSS3', 'HTML5',
    'Git', 'MongoDB', 'PostgreSQL', 'AWS'
  ]

  return (
    <section id="about" className="about">
      <div className="about-container">
        <div className="about-content">
          <div className="about-text">
            <h2 className="section-title">About Me</h2>
            <p className="about-description">
              I'm a passionate software developer with a focus on creating 
              immersive web experiences. With expertise in modern JavaScript 
              frameworks and 3D web technologies, I bring ideas to life through 
              code and creativity.
            </p>
            <p className="about-description">
              My journey in technology spans from traditional web development 
              to cutting-edge 3D graphics and interactive experiences. I believe 
              in the power of technology to create meaningful connections and 
              solve real-world problems.
            </p>
            
            <div className="skills-section">
              <h3>Technologies & Skills</h3>
              <div className="skills-grid">
                {skills.map((skill, index) => (
                  <span key={index} className="skill-tag">
                    {skill}
                  </span>
                ))}
              </div>
            </div>
          </div>
          
          <div className="about-visual">
            <div className="profile-card">
              <div className="profile-image">
                <div className="image-placeholder">
                  <span>MG</span>
                </div>
              </div>
              <div className="profile-info">
                <h3>Mark Gatere</h3>
                <p>Software Developer</p>
                <div className="profile-stats">
                  <div className="stat">
                    <span className="stat-number">5+</span>
                    <span className="stat-label">Years Experience</span>
                  </div>
                  <div className="stat">
                    <span className="stat-number">50+</span>
                    <span className="stat-label">Projects Completed</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}

export default About
