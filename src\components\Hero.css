.hero {
  min-height: 100vh;
  display: flex;
  align-items: center;
  background: linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 50%, #0a0a0a 100%);
  position: relative;
  overflow: hidden;
}

.hero::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 30% 20%, rgba(100, 108, 255, 0.1) 0%, transparent 50%),
              radial-gradient(circle at 70% 80%, rgba(100, 108, 255, 0.05) 0%, transparent 50%);
  pointer-events: none;
}

.hero-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
  width: 100%;
}

.hero-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  align-items: center;
  min-height: 80vh;
}

.hero-text {
  z-index: 2;
}

.hero-title {
  font-size: clamp(2.5rem, 5vw, 4rem);
  font-weight: 700;
  line-height: 1.1;
  margin-bottom: 1rem;
  color: #fff;
}

.gradient-text {
  background: linear-gradient(135deg, #646cff 0%, #9333ea 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.hero-subtitle {
  font-size: 1.25rem;
  color: #a0a0a0;
  margin-bottom: 1.5rem;
  font-weight: 500;
}

.hero-description {
  font-size: 1.1rem;
  color: #d0d0d0;
  line-height: 1.6;
  margin-bottom: 2.5rem;
  max-width: 500px;
}

.hero-actions {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.btn {
  padding: 0.875rem 2rem;
  border-radius: 8px;
  text-decoration: none;
  font-weight: 600;
  font-size: 1rem;
  transition: all 0.3s ease;
  display: inline-block;
  text-align: center;
  min-width: 140px;
}

.btn-primary {
  background: linear-gradient(135deg, #646cff 0%, #9333ea 100%);
  color: #fff;
  border: none;
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(100, 108, 255, 0.3);
}

.btn-secondary {
  background: transparent;
  color: #fff;
  border: 2px solid #646cff;
}

.btn-secondary:hover {
  background: #646cff;
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(100, 108, 255, 0.2);
}

.hero-visual {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 600px;
  position: relative;
}

.card-container {
  width: 100%;
  height: 100%;
  position: relative;
}

@media (max-width: 768px) {
  .hero-content {
    grid-template-columns: 1fr;
    gap: 2rem;
    text-align: center;
  }

  .hero-visual {
    height: 400px;
    order: -1;
  }

  .hero-actions {
    justify-content: center;
  }

  .btn {
    min-width: 120px;
    padding: 0.75rem 1.5rem;
  }
}

@media (max-width: 480px) {
  .hero-actions {
    flex-direction: column;
    align-items: center;
  }

  .btn {
    width: 200px;
  }
}
