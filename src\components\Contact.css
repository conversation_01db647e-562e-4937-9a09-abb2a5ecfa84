.contact {
  padding: 6rem 0;
  background: #0a0a0a;
  position: relative;
}

.contact::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 70% 30%, rgba(100, 108, 255, 0.05) 0%, transparent 50%);
  pointer-events: none;
}

.contact-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
}

.contact-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  align-items: start;
}

.contact-description {
  font-size: 1.1rem;
  line-height: 1.7;
  color: #d0d0d0;
  margin-bottom: 2.5rem;
}

.contact-methods {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.contact-method {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
}

.contact-method:hover {
  background: rgba(255, 255, 255, 0.08);
  transform: translateX(10px);
}

.method-icon {
  font-size: 1.5rem;
  width: 50px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #646cff 0%, #9333ea 100%);
  border-radius: 10px;
}

.method-info h3 {
  color: #fff;
  font-size: 1rem;
  margin-bottom: 0.25rem;
  font-weight: 600;
}

.method-info p {
  color: #a0a0a0;
  font-size: 0.9rem;
  margin: 0;
}

.contact-form-container {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  padding: 2.5rem;
}

.contact-form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.form-group label {
  color: #fff;
  font-weight: 500;
  font-size: 0.9rem;
}

.form-group input,
.form-group textarea {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  padding: 0.875rem;
  color: #fff;
  font-size: 1rem;
  transition: all 0.3s ease;
  resize: vertical;
}

.form-group input:focus,
.form-group textarea:focus {
  outline: none;
  border-color: #646cff;
  background: rgba(255, 255, 255, 0.15);
  box-shadow: 0 0 0 3px rgba(100, 108, 255, 0.1);
}

.form-group input::placeholder,
.form-group textarea::placeholder {
  color: #a0a0a0;
}

.contact-form .btn {
  margin-top: 1rem;
  align-self: flex-start;
  padding: 0.875rem 2rem;
  border-radius: 8px;
  text-decoration: none;
  font-weight: 600;
  font-size: 1rem;
  transition: all 0.3s ease;
  display: inline-block;
  text-align: center;
  min-width: 140px;
  border: none;
  cursor: pointer;
}

.contact-form .btn-primary {
  background: linear-gradient(135deg, #646cff 0%, #9333ea 100%);
  color: #fff;
}

.contact-form .btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(100, 108, 255, 0.3);
}

@media (max-width: 768px) {
  .contact-content {
    grid-template-columns: 1fr;
    gap: 3rem;
  }

  .contact-form-container {
    padding: 2rem;
  }

  .contact-method {
    flex-direction: column;
    text-align: center;
    gap: 0.75rem;
  }

  .contact-method:hover {
    transform: translateY(-5px);
  }

  .contact-form .btn {
    align-self: stretch;
  }
}
